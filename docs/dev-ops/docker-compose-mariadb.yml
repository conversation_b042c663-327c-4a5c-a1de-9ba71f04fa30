version: '3.8'

services:
  mariadb-vector:
    image: mariadb:11.7
    container_name: mariadb-vector
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: vector_db
      MYSQL_USER: vector_user
      MYSQL_PASSWORD: vector_pass
    ports:
      - "3307:3306"
    volumes:
      - mariadb_vector_data:/var/lib/mysql
      - ./mariadb/init:/docker-entrypoint-initdb.d
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-time-zone='+8:00'
      --innodb-buffer-pool-size=256M
      --max-connections=200
    networks:
      - ai-agent-network

  mysql-primary:
    image: mysql:8.0
    container_name: mysql-primary
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: xfg_frame_archetype
      MYSQL_USER: ai_user
      MYSQL_PASSWORD: ai_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_primary_data:/var/lib/mysql
      - ./mysql/sql:/docker-entrypoint-initdb.d
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-time-zone='+8:00'
      --innodb-buffer-pool-size=256M
      --max-connections=200
    networks:
      - ai-agent-network

volumes:
  mariadb_vector_data:
    driver: local
  mysql_primary_data:
    driver: local

networks:
  ai-agent-network:
    driver: bridge
