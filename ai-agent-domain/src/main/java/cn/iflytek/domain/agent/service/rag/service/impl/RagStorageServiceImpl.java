package cn.iflytek.domain.rag.service.impl;

import cn.iflytek.domain.agent.model.AiRagOrder;
import cn.iflytek.domain.rag.service.IRagStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * RAG存储服务实现
 */
@Slf4j
@Service
public class RagStorageServiceImpl implements IRagStorageService {

    @Resource
    private VectorStore vectorStore;

    @Override
    public boolean createKnowledgeBase(AiRagOrder ragOrder) {
        try {
            log.info("创建知识库: {}, 标签: {}", ragOrder.getRagName(), ragOrder.getKnowledgeTag());
            
            // 创建一个标识文档来表示知识库的存在
            Document knowledgeBaseDoc = new Document(
                "knowledge_base_" + ragOrder.getRagName(),
                "Knowledge Base: " + ragOrder.getRagName(),
                Map.of(
                    "type", "knowledge_base",
                    "rag_name", ragOrder.getRagName(),
                    "knowledge_tag", ragOrder.getKnowledgeTag() != null ? ragOrder.getKnowledgeTag() : "",
                    "created_at", System.currentTimeMillis()
                )
            );
            
            vectorStore.add(List.of(knowledgeBaseDoc));
            log.info("知识库创建成功: {}", ragOrder.getRagName());
            return true;
        } catch (Exception e) {
            log.error("创建知识库失败: {}", ragOrder.getRagName(), e);
            return false;
        }
    }

    @Override
    public boolean deleteKnowledgeBase(String ragName) {
        try {
            log.info("删除知识库: {}", ragName);
            
            // 删除知识库中的所有文档
            FilterExpressionBuilder builder = new FilterExpressionBuilder();
            String filterExpression = builder.eq("rag_name", ragName).build();
            
            List<Document> documents = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("*")
                    .topK(10000)
                    .filterExpression(filterExpression)
                    .build()
            );
            
            if (!documents.isEmpty()) {
                List<String> documentIds = documents.stream()
                    .map(Document::getId)
                    .collect(Collectors.toList());
                vectorStore.delete(documentIds);
            }
            
            log.info("知识库删除成功: {}, 删除文档数量: {}", ragName, documents.size());
            return true;
        } catch (Exception e) {
            log.error("删除知识库失败: {}", ragName, e);
            return false;
        }
    }

    @Override
    public boolean addDocuments(String ragName, List<Document> documents) {
        try {
            log.info("向知识库 {} 添加 {} 个文档", ragName, documents.size());
            
            // 为每个文档添加知识库标识
            List<Document> enrichedDocuments = documents.stream()
                .map(doc -> {
                    Map<String, Object> metadata = new HashMap<>(doc.getMetadata());
                    metadata.put("rag_name", ragName);
                    metadata.put("type", "document");
                    metadata.put("added_at", System.currentTimeMillis());
                    
                    return new Document(doc.getId(), doc.getContent(), metadata);
                })
                .collect(Collectors.toList());
            
            vectorStore.add(enrichedDocuments);
            log.info("文档添加成功: 知识库={}, 文档数量={}", ragName, documents.size());
            return true;
        } catch (Exception e) {
            log.error("添加文档失败: 知识库={}, 文档数量={}", ragName, documents.size(), e);
            return false;
        }
    }

    @Override
    public boolean deleteDocuments(String ragName, List<String> documentIds) {
        try {
            log.info("从知识库 {} 删除 {} 个文档", ragName, documentIds.size());
            
            // 验证文档是否属于指定知识库
            FilterExpressionBuilder builder = new FilterExpressionBuilder();
            String filterExpression = builder.and(
                builder.eq("rag_name", ragName),
                builder.in("id", documentIds.toArray(new String[0]))
            ).build();
            
            List<Document> documentsToDelete = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("*")
                    .topK(documentIds.size())
                    .filterExpression(filterExpression)
                    .build()
            );
            
            if (!documentsToDelete.isEmpty()) {
                List<String> validDocumentIds = documentsToDelete.stream()
                    .map(Document::getId)
                    .collect(Collectors.toList());
                vectorStore.delete(validDocumentIds);
                log.info("文档删除成功: 知识库={}, 删除数量={}", ragName, validDocumentIds.size());
            }
            
            return true;
        } catch (Exception e) {
            log.error("删除文档失败: 知识库={}, 文档ID={}", ragName, documentIds, e);
            return false;
        }
    }

    @Override
    public List<Document> vectorSearch(String ragName, String query, int topK, 
                                      double similarityThreshold, String filterExpression) {
        try {
            log.debug("向量搜索: 知识库={}, 查询={}, topK={}, 阈值={}", ragName, query, topK, similarityThreshold);
            
            // 构建过滤表达式
            FilterExpressionBuilder builder = new FilterExpressionBuilder();
            String ragFilter = builder.and(
                builder.eq("rag_name", ragName),
                builder.eq("type", "document")
            ).build();
            
            // 如果有额外的过滤表达式，则组合
            String finalFilter = ragFilter;
            if (StringUtils.hasText(filterExpression)) {
                finalFilter = builder.and(
                    builder.group(ragFilter),
                    builder.group(filterExpression)
                ).build();
            }
            
            SearchRequest searchRequest = SearchRequest.builder()
                .query(query)
                .topK(topK)
                .similarityThreshold(similarityThreshold)
                .filterExpression(finalFilter)
                .build();
            
            List<Document> results = vectorStore.similaritySearch(searchRequest);
            log.debug("搜索完成: 知识库={}, 结果数量={}", ragName, results.size());
            
            return results;
        } catch (Exception e) {
            log.error("向量搜索失败: 知识库={}, 查询={}", ragName, query, e);
            return List.of();
        }
    }

    @Override
    public List<Document> getAllDocuments(String ragName) {
        try {
            FilterExpressionBuilder builder = new FilterExpressionBuilder();
            String filterExpression = builder.and(
                builder.eq("rag_name", ragName),
                builder.eq("type", "document")
            ).build();
            
            return vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("*")
                    .topK(10000)
                    .filterExpression(filterExpression)
                    .build()
            );
        } catch (Exception e) {
            log.error("获取所有文档失败: 知识库={}", ragName, e);
            return List.of();
        }
    }

    @Override
    public boolean knowledgeBaseExists(String ragName) {
        try {
            FilterExpressionBuilder builder = new FilterExpressionBuilder();
            String filterExpression = builder.and(
                builder.eq("rag_name", ragName),
                builder.eq("type", "knowledge_base")
            ).build();
            
            List<Document> results = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("*")
                    .topK(1)
                    .filterExpression(filterExpression)
                    .build()
            );
            
            return !results.isEmpty();
        } catch (Exception e) {
            log.error("检查知识库存在性失败: {}", ragName, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getKnowledgeBaseStats(String ragName) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 获取文档数量
            List<Document> documents = getAllDocuments(ragName);
            stats.put("document_count", documents.size());
            
            // 获取知识库信息
            FilterExpressionBuilder builder = new FilterExpressionBuilder();
            String filterExpression = builder.and(
                builder.eq("rag_name", ragName),
                builder.eq("type", "knowledge_base")
            ).build();
            
            List<Document> kbDocs = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("*")
                    .topK(1)
                    .filterExpression(filterExpression)
                    .build()
            );
            
            if (!kbDocs.isEmpty()) {
                Document kbDoc = kbDocs.get(0);
                stats.put("knowledge_tag", kbDoc.getMetadata().get("knowledge_tag"));
                stats.put("created_at", kbDoc.getMetadata().get("created_at"));
            }
            
            stats.put("exists", !kbDocs.isEmpty());
            
        } catch (Exception e) {
            log.error("获取知识库统计信息失败: {}", ragName, e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    @Override
    public boolean clearKnowledgeBase(String ragName) {
        try {
            log.info("清空知识库: {}", ragName);
            
            // 只删除文档，保留知识库标识
            FilterExpressionBuilder builder = new FilterExpressionBuilder();
            String filterExpression = builder.and(
                builder.eq("rag_name", ragName),
                builder.eq("type", "document")
            ).build();
            
            List<Document> documents = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("*")
                    .topK(10000)
                    .filterExpression(filterExpression)
                    .build()
            );
            
            if (!documents.isEmpty()) {
                List<String> documentIds = documents.stream()
                    .map(Document::getId)
                    .collect(Collectors.toList());
                vectorStore.delete(documentIds);
            }
            
            log.info("知识库清空成功: {}, 删除文档数量: {}", ragName, documents.size());
            return true;
        } catch (Exception e) {
            log.error("清空知识库失败: {}", ragName, e);
            return false;
        }
    }
}
