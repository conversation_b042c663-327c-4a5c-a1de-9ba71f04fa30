package cn.iflytek.domain.rag.util;

import org.springframework.ai.document.Document;
import org.springframework.ai.reader.TextReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 文档处理工具类
 */
@Component
public class DocumentProcessor {

    /**
     * 将文本内容转换为文档列表
     * @param content 文本内容
     * @param metadata 元数据
     * @param chunkSize 分块大小
     * @param chunkOverlap 分块重叠
     * @return 文档列表
     */
    public List<Document> processTextContent(String content, Map<String, Object> metadata, 
                                           int chunkSize, int chunkOverlap) {
        try {
            // 创建文本资源
            Resource resource = new ByteArrayResource(content.getBytes());
            
            // 使用TextReader读取文档
            TextReader textReader = new TextReader(resource);
            List<Document> documents = textReader.get();
            
            // 如果需要分块处理
            if (chunkSize > 0 && content.length() > chunkSize) {
                TokenTextSplitter splitter = new TokenTextSplitter(chunkSize, chunkOverlap, 5, 10000, true);
                documents = splitter.apply(documents);
            }
            
            // 为每个文档添加元数据和唯一ID
            for (int i = 0; i < documents.size(); i++) {
                Document doc = documents.get(i);
                Map<String, Object> docMetadata = doc.getMetadata();
                
                // 添加原始元数据
                if (metadata != null) {
                    docMetadata.putAll(metadata);
                }
                
                // 添加分块信息
                docMetadata.put("chunk_index", i);
                docMetadata.put("total_chunks", documents.size());
                docMetadata.put("processed_at", System.currentTimeMillis());
                
                // 如果没有ID，生成一个
                if (doc.getId() == null || doc.getId().isEmpty()) {
                    documents.set(i, new Document(UUID.randomUUID().toString(), doc.getContent(), docMetadata));
                }
            }
            
            return documents;
        } catch (Exception e) {
            throw new RuntimeException("处理文本内容失败", e);
        }
    }

    /**
     * 将单个文本转换为文档
     * @param content 文本内容
     * @param metadata 元数据
     * @return 文档
     */
    public Document createDocument(String content, Map<String, Object> metadata) {
        String id = UUID.randomUUID().toString();
        Map<String, Object> docMetadata = metadata != null ? metadata : Map.of();
        return new Document(id, content, docMetadata);
    }

    /**
     * 创建带ID的文档
     * @param id 文档ID
     * @param content 文本内容
     * @param metadata 元数据
     * @return 文档
     */
    public Document createDocument(String id, String content, Map<String, Object> metadata) {
        Map<String, Object> docMetadata = metadata != null ? metadata : Map.of();
        return new Document(id, content, docMetadata);
    }

    /**
     * 验证文档内容
     * @param document 文档
     * @return 是否有效
     */
    public boolean validateDocument(Document document) {
        if (document == null) {
            return false;
        }
        
        if (document.getContent() == null || document.getContent().trim().isEmpty()) {
            return false;
        }
        
        if (document.getId() == null || document.getId().trim().isEmpty()) {
            return false;
        }
        
        return true;
    }

    /**
     * 清理文档内容
     * @param content 原始内容
     * @return 清理后的内容
     */
    public String cleanContent(String content) {
        if (content == null) {
            return "";
        }
        
        // 移除多余的空白字符
        content = content.replaceAll("\\s+", " ");
        
        // 移除特殊字符（可根据需要调整）
        content = content.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");
        
        return content.trim();
    }

    /**
     * 合并文档元数据
     * @param baseMetadata 基础元数据
     * @param additionalMetadata 额外元数据
     * @return 合并后的元数据
     */
    public Map<String, Object> mergeMetadata(Map<String, Object> baseMetadata, 
                                           Map<String, Object> additionalMetadata) {
        Map<String, Object> merged = baseMetadata != null ? 
            new java.util.HashMap<>(baseMetadata) : new java.util.HashMap<>();
        
        if (additionalMetadata != null) {
            merged.putAll(additionalMetadata);
        }
        
        return merged;
    }
}
