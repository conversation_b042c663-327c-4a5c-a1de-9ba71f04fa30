package cn.iflytek.domain.rag.service;

import cn.iflytek.domain.agent.model.AiRagOrder;
import org.springframework.ai.document.Document;

import java.util.List;
import java.util.Map;

/**
 * RAG存储服务接口
 */
public interface IRagStorageService {
    
    /**
     * 创建知识库
     * @param ragOrder RAG订单信息
     * @return 是否成功
     */
    boolean createKnowledgeBase(AiRagOrder ragOrder);
    
    /**
     * 删除知识库
     * @param ragName 知识库名称
     * @return 是否成功
     */
    boolean deleteKnowledgeBase(String ragName);
    
    /**
     * 添加文档到知识库
     * @param ragName 知识库名称
     * @param documents 文档列表
     * @return 是否成功
     */
    boolean addDocuments(String ragName, List<Document> documents);
    
    /**
     * 从知识库删除文档
     * @param ragName 知识库名称
     * @param documentIds 文档ID列表
     * @return 是否成功
     */
    boolean deleteDocuments(String ragName, List<String> documentIds);
    
    /**
     * 向量搜索
     * @param ragName 知识库名称
     * @param query 查询文本
     * @param topK 返回结果数量
     * @param similarityThreshold 相似度阈值
     * @param filterExpression 过滤表达式
     * @return 搜索结果
     */
    List<Document> vectorSearch(String ragName, String query, int topK, 
                               double similarityThreshold, String filterExpression);
    
    /**
     * 获取知识库中的所有文档
     * @param ragName 知识库名称
     * @return 文档列表
     */
    List<Document> getAllDocuments(String ragName);
    
    /**
     * 检查知识库是否存在
     * @param ragName 知识库名称
     * @return 是否存在
     */
    boolean knowledgeBaseExists(String ragName);
    
    /**
     * 获取知识库统计信息
     * @param ragName 知识库名称
     * @return 统计信息
     */
    Map<String, Object> getKnowledgeBaseStats(String ragName);
    
    /**
     * 清空知识库
     * @param ragName 知识库名称
     * @return 是否成功
     */
    boolean clearKnowledgeBase(String ragName);
}
