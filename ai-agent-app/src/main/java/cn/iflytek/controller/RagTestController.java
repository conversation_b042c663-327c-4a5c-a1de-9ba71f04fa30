package cn.iflytek.controller;

import cn.iflytek.domain.agent.model.AiRagOrder;
import cn.iflytek.domain.rag.service.IRagStorageService;
import cn.iflytek.domain.rag.util.DocumentProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.QuestionAnswerAdvisor;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RAG测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/rag-test")
public class RagTestController {

    @Resource
    private IRagStorageService ragStorageService;

    @Resource
    private DocumentProcessor documentProcessor;

    @Resource
    private VectorStore vectorStore;

    @Resource(required = false)
    private ChatClient.Builder chatClientBuilder;

    /**
     * 测试创建知识库并添加示例文档
     */
    @PostMapping("/create-sample-knowledge-base")
    public Map<String, Object> createSampleKnowledgeBase(@RequestParam(defaultValue = "test-kb") String ragName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建知识库
            AiRagOrder ragOrder = AiRagOrder.builder()
                .ragName(ragName)
                .knowledgeTag("test")
                .build();
            
            boolean created = ragStorageService.createKnowledgeBase(ragOrder);
            if (!created) {
                result.put("success", false);
                result.put("message", "知识库创建失败");
                return result;
            }
            
            // 添加示例文档
            List<Document> sampleDocuments = List.of(
                documentProcessor.createDocument(
                    "doc1",
                    "Spring AI是一个用于构建AI应用程序的框架。它提供了与各种AI模型的集成，包括OpenAI、Azure OpenAI等。",
                    Map.of("category", "framework", "topic", "spring-ai")
                ),
                documentProcessor.createDocument(
                    "doc2", 
                    "向量数据库是一种专门用于存储和检索高维向量数据的数据库。它支持相似性搜索和语义搜索。",
                    Map.of("category", "database", "topic", "vector-db")
                ),
                documentProcessor.createDocument(
                    "doc3",
                    "RAG（检索增强生成）是一种结合了信息检索和文本生成的AI技术。它可以提高生成内容的准确性和相关性。",
                    Map.of("category", "technology", "topic", "rag")
                ),
                documentProcessor.createDocument(
                    "doc4",
                    "MariaDB是一个开源的关系型数据库管理系统，是MySQL的一个分支。最新版本支持向量存储功能。",
                    Map.of("category", "database", "topic", "mariadb")
                )
            );
            
            boolean added = ragStorageService.addDocuments(ragName, sampleDocuments);
            
            result.put("success", added);
            result.put("message", added ? "示例知识库创建成功" : "文档添加失败");
            result.put("ragName", ragName);
            result.put("documentCount", sampleDocuments.size());
            
        } catch (Exception e) {
            log.error("创建示例知识库失败", e);
            result.put("success", false);
            result.put("message", "创建失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试向量搜索
     */
    @GetMapping("/test-search")
    public Map<String, Object> testSearch(@RequestParam(defaultValue = "test-kb") String ragName,
                                         @RequestParam String query,
                                         @RequestParam(defaultValue = "3") int topK) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Document> searchResults = ragStorageService.vectorSearch(
                ragName, query, topK, 0.0, null);
            
            result.put("success", true);
            result.put("query", query);
            result.put("ragName", ragName);
            result.put("resultCount", searchResults.size());
            result.put("results", searchResults.stream()
                .map(doc -> Map.of(
                    "id", doc.getId(),
                    "content", doc.getContent(),
                    "metadata", doc.getMetadata(),
                    "score", doc.getMetadata().getOrDefault("distance", "N/A")
                ))
                .toList());
            
        } catch (Exception e) {
            log.error("测试搜索失败", e);
            result.put("success", false);
            result.put("message", "搜索失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试RAG问答
     */
    @PostMapping("/test-qa")
    public Map<String, Object> testQuestionAnswer(@RequestParam(defaultValue = "test-kb") String ragName,
                                                 @RequestParam String question) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (chatClientBuilder == null) {
                result.put("success", false);
                result.put("message", "ChatClient未配置，无法进行问答测试");
                return result;
            }
            
            // 创建RAG顾问
            QuestionAnswerAdvisor qaAdvisor = QuestionAnswerAdvisor.builder(vectorStore)
                .searchRequest(SearchRequest.builder()
                    .topK(3)
                    .similarityThreshold(0.0)
                    .filterExpression("rag_name == '" + ragName + "' && type == 'document'")
                    .build())
                .build();
            
            // 构建ChatClient并进行问答
            ChatClient chatClient = chatClientBuilder
                .defaultAdvisors(qaAdvisor)
                .build();
            
            String answer = chatClient.prompt()
                .user(question)
                .call()
                .content();
            
            result.put("success", true);
            result.put("question", question);
            result.put("answer", answer);
            result.put("ragName", ragName);
            
        } catch (Exception e) {
            log.error("RAG问答测试失败", e);
            result.put("success", false);
            result.put("message", "问答失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试向量存储状态
     */
    @GetMapping("/test-vector-store")
    public Map<String, Object> testVectorStore() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试向量存储连接
            List<Document> allDocs = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("test")
                    .topK(1)
                    .build()
            );
            
            result.put("success", true);
            result.put("vectorStoreClass", vectorStore.getClass().getSimpleName());
            result.put("message", "向量存储连接正常");
            result.put("testQueryResults", allDocs.size());
            
        } catch (Exception e) {
            log.error("向量存储测试失败", e);
            result.put("success", false);
            result.put("message", "向量存储测试失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 批量添加文档测试
     */
    @PostMapping("/test-batch-add")
    public Map<String, Object> testBatchAdd(@RequestParam(defaultValue = "test-kb") String ragName,
                                           @RequestParam(defaultValue = "10") int documentCount) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 生成测试文档
            List<Document> testDocuments = new java.util.ArrayList<>();
            for (int i = 1; i <= documentCount; i++) {
                Document doc = documentProcessor.createDocument(
                    "batch_doc_" + i,
                    "这是第" + i + "个测试文档。内容包含了一些示例文本，用于测试批量添加功能。文档编号：" + i,
                    Map.of(
                        "batch", "test",
                        "index", i,
                        "category", "test-document"
                    )
                );
                testDocuments.add(doc);
            }
            
            boolean success = ragStorageService.addDocuments(ragName, testDocuments);
            
            result.put("success", success);
            result.put("message", success ? "批量添加成功" : "批量添加失败");
            result.put("ragName", ragName);
            result.put("addedCount", documentCount);
            
        } catch (Exception e) {
            log.error("批量添加测试失败", e);
            result.put("success", false);
            result.put("message", "批量添加失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 清理测试数据
     */
    @DeleteMapping("/cleanup")
    public Map<String, Object> cleanup(@RequestParam(defaultValue = "test-kb") String ragName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = ragStorageService.deleteKnowledgeBase(ragName);
            
            result.put("success", success);
            result.put("message", success ? "测试数据清理成功" : "清理失败");
            result.put("ragName", ragName);
            
        } catch (Exception e) {
            log.error("清理测试数据失败", e);
            result.put("success", false);
            result.put("message", "清理失败: " + e.getMessage());
        }
        
        return result;
    }
}
